using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Alpaca.Markets;

namespace SmaTrendFollower.Models;

/// <summary>
/// Entity representing a cached index bar in SQLite database.
/// Stores historical index data to avoid re-downloading unchanged history from Polygon API.
/// </summary>
[Table("CachedIndexBars")]
[Index(nameof(Symbol), nameof(TimeUtc), IsUnique = true)]
public class CachedIndexBar
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Index symbol (e.g., "I:SPX", "I:VIX")
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Bar timestamp in UTC (converted from Polygon's milliseconds since epoch)
    /// </summary>
    [Required]
    public DateTime TimeUtc { get; set; }

    /// <summary>
    /// Opening price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Open { get; set; }

    /// <summary>
    /// Highest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal High { get; set; }

    /// <summary>
    /// Lowest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Low { get; set; }

    /// <summary>
    /// Closing price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Close { get; set; }

    /// <summary>
    /// Trading volume for the time period
    /// </summary>
    [Required]
    public long Volume { get; set; }

    /// <summary>
    /// When this record was cached (for cache management)
    /// </summary>
    [Required]
    public DateTime CachedAt { get; set; }

    /// <summary>
    /// Converts this cached entity to an IndexBar record
    /// </summary>
    public Services.IndexBar ToIndexBar()
    {
        return new Services.IndexBar(TimeUtc, Open, High, Low, Close, Volume);
    }

    /// <summary>
    /// Creates a CachedIndexBar from an IndexBar and symbol
    /// </summary>
    public static CachedIndexBar FromIndexBar(string symbol, Services.IndexBar indexBar)
    {
        return new CachedIndexBar
        {
            Symbol = symbol,
            TimeUtc = indexBar.TimeUtc,
            Open = indexBar.Open,
            High = indexBar.High,
            Low = indexBar.Low,
            Close = indexBar.Close,
            Volume = indexBar.Volume,
            CachedAt = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Metadata about cached data for each symbol to track cache freshness
/// </summary>
[Table("CacheMetadata")]
public class CacheMetadata
{
    [Key]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Latest date for which we have cached data
    /// </summary>
    [Required]
    public DateTime LatestDataDate { get; set; }

    /// <summary>
    /// When the cache was last updated
    /// </summary>
    [Required]
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Total number of cached bars for this symbol
    /// </summary>
    [Required]
    public int BarCount { get; set; }
}

/// <summary>
/// Entity representing a cached stock/ETF bar in SQLite database.
/// Stores historical stock data to avoid re-downloading unchanged history from Alpaca/Polygon APIs.
/// Supports both daily and minute timeframes with 1-year retention.
/// </summary>
[Table("CachedStockBars")]
[Index(nameof(Symbol), nameof(TimeFrame), nameof(TimeUtc), IsUnique = true)]
public class CachedStockBar
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Stock/ETF symbol (e.g., "AAPL", "SPY")
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Timeframe: "Day" or "Minute"
    /// </summary>
    [Required]
    [MaxLength(10)]
    public string TimeFrame { get; set; } = string.Empty;

    /// <summary>
    /// Bar timestamp in UTC (consistent with Alpaca format)
    /// </summary>
    [Required]
    public DateTime TimeUtc { get; set; }

    /// <summary>
    /// Opening price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Open { get; set; }

    /// <summary>
    /// Highest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal High { get; set; }

    /// <summary>
    /// Lowest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Low { get; set; }

    /// <summary>
    /// Closing price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Close { get; set; }

    /// <summary>
    /// Trading volume for the time period
    /// </summary>
    [Required]
    public long Volume { get; set; }

    /// <summary>
    /// Volume-weighted average price (if available)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? Vwap { get; set; }

    /// <summary>
    /// Number of trades (if available)
    /// </summary>
    public ulong? TradeCount { get; set; }

    /// <summary>
    /// When this record was cached (for cache management)
    /// </summary>
    [Required]
    public DateTime CachedAt { get; set; }

    /// <summary>
    /// Indicates if this bar data is compressed (for bars older than 30 days)
    /// </summary>
    public bool IsCompressed { get; set; }

    /// <summary>
    /// Compressed bar data (used when IsCompressed = true)
    /// Stores JSON representation of OHLCV data in compressed format
    /// </summary>
    public byte[]? CompressedData { get; set; }

    /// <summary>
    /// Original data size before compression (for monitoring compression efficiency)
    /// </summary>
    public int? OriginalDataSize { get; set; }

    /// <summary>
    /// Converts this cached entity to an IBar interface
    /// </summary>
    public CachedBarWrapper ToIBar()
    {
        return new CachedBarWrapper(this);
    }

    /// <summary>
    /// Creates a CachedStockBar from an IBar and metadata
    /// </summary>
    public static CachedStockBar FromIBar(string symbol, string timeFrame, IBar bar, bool shouldCompress = false)
    {
        var cachedBar = new CachedStockBar
        {
            Symbol = symbol,
            TimeFrame = timeFrame,
            TimeUtc = bar.TimeUtc,
            CachedAt = DateTime.UtcNow,
            IsCompressed = shouldCompress
        };

        if (shouldCompress)
        {
            // Store compressed data for older bars
            var barData = new
            {
                Open = bar.Open,
                High = bar.High,
                Low = bar.Low,
                Close = bar.Close,
                Volume = (long)bar.Volume,
                Vwap = bar.Vwap > 0 ? bar.Vwap : (decimal?)null,
                TradeCount = bar.TradeCount > 0 ? bar.TradeCount : (ulong?)null
            };

            var jsonData = System.Text.Json.JsonSerializer.Serialize(barData);
            cachedBar.OriginalDataSize = System.Text.Encoding.UTF8.GetByteCount(jsonData);
            // Note: Compression will be handled by the service layer
        }
        else
        {
            // Store uncompressed data for recent bars
            cachedBar.Open = bar.Open;
            cachedBar.High = bar.High;
            cachedBar.Low = bar.Low;
            cachedBar.Close = bar.Close;
            cachedBar.Volume = (long)bar.Volume;
            cachedBar.Vwap = bar.Vwap > 0 ? bar.Vwap : null;
            cachedBar.TradeCount = bar.TradeCount > 0 ? bar.TradeCount : null;
        }

        return cachedBar;
    }
}

/// <summary>
/// Wrapper class that implements IBar interface for cached stock data compatibility.
/// Ensures cached bars can be used interchangeably with live API bars.
/// Handles both compressed and uncompressed data transparently.
/// </summary>
public class CachedBarWrapper : IBar
{
    private readonly CachedStockBar _cachedBar;
    private readonly Lazy<BarData> _decompressedData;

    public CachedBarWrapper(CachedStockBar cachedBar, Services.IDataCompressionService? compressionService = null)
    {
        _cachedBar = cachedBar;
        _decompressedData = new Lazy<BarData>(() => GetBarData(compressionService));
    }

    public string Symbol => _cachedBar.Symbol;
    public DateTime TimeUtc => _cachedBar.TimeUtc;
    public decimal Open => _cachedBar.IsCompressed ? _decompressedData.Value.Open : _cachedBar.Open;
    public decimal High => _cachedBar.IsCompressed ? _decompressedData.Value.High : _cachedBar.High;
    public decimal Low => _cachedBar.IsCompressed ? _decompressedData.Value.Low : _cachedBar.Low;
    public decimal Close => _cachedBar.IsCompressed ? _decompressedData.Value.Close : _cachedBar.Close;
    public decimal Volume => _cachedBar.IsCompressed ? _decompressedData.Value.Volume : _cachedBar.Volume;
    public decimal Vwap => _cachedBar.IsCompressed ? (_decompressedData.Value.Vwap ?? 0) : (_cachedBar.Vwap ?? 0);
    public ulong TradeCount => _cachedBar.IsCompressed ? (_decompressedData.Value.TradeCount ?? 0) : (_cachedBar.TradeCount ?? 0);

    private BarData GetBarData(Services.IDataCompressionService? compressionService)
    {
        if (!_cachedBar.IsCompressed || _cachedBar.CompressedData == null)
        {
            return new BarData
            {
                Open = _cachedBar.Open,
                High = _cachedBar.High,
                Low = _cachedBar.Low,
                Close = _cachedBar.Close,
                Volume = _cachedBar.Volume,
                Vwap = _cachedBar.Vwap,
                TradeCount = _cachedBar.TradeCount
            };
        }

        if (compressionService == null)
        {
            throw new InvalidOperationException("Compression service required for compressed bar data");
        }

        var jsonData = compressionService.DecompressToJson(_cachedBar.CompressedData);
        var barData = System.Text.Json.JsonSerializer.Deserialize<BarData>(jsonData);
        return barData ?? new BarData();
    }

    private class BarData
    {
        public decimal Open { get; set; }
        public decimal High { get; set; }
        public decimal Low { get; set; }
        public decimal Close { get; set; }
        public long Volume { get; set; }
        public decimal? Vwap { get; set; }
        public ulong? TradeCount { get; set; }
    }
}

/// <summary>
/// Metadata about cached stock data for each symbol and timeframe to track cache freshness
/// </summary>
[Table("StockCacheMetadata")]
public class StockCacheMetadata
{
    [Key]
    [MaxLength(50)]
    public string CacheKey { get; set; } = string.Empty; // Format: "SYMBOL_TIMEFRAME" (e.g., "AAPL_Day")

    /// <summary>
    /// Stock/ETF symbol
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Timeframe: "Day" or "Minute"
    /// </summary>
    [Required]
    [MaxLength(10)]
    public string TimeFrame { get; set; } = string.Empty;

    /// <summary>
    /// Latest date for which we have cached data
    /// </summary>
    [Required]
    public DateTime LatestDataDate { get; set; }

    /// <summary>
    /// Earliest date for which we have cached data
    /// </summary>
    [Required]
    public DateTime EarliestDataDate { get; set; }

    /// <summary>
    /// When the cache was last updated
    /// </summary>
    [Required]
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Total number of bars cached for this symbol and timeframe
    /// </summary>
    [Required]
    public int BarCount { get; set; }

    /// <summary>
    /// Creates a cache key from symbol and timeframe
    /// </summary>
    public static string CreateCacheKey(string symbol, string timeFrame)
    {
        return $"{symbol}_{timeFrame}";
    }
}
