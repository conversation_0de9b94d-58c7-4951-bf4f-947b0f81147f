using SmaTrendFollower.Services;
using SmaTrendFollower.Console;
using SmaTrendFollower.Data;
using DotNetEnv;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Serilog;

// Load environment variables from .env file
var envPath = ".env";
if (File.Exists(envPath))
{
    Env.Load(envPath);

    // Force reload by reading the file manually to ensure variables are set
    var lines = File.ReadAllLines(envPath);
    foreach (var line in lines)
    {
        if (line.StartsWith("#") || string.IsNullOrWhiteSpace(line)) continue;

        var parts = line.Split('=', 2);
        if (parts.Length == 2)
        {
            Environment.SetEnvironmentVariable(parts[0].Trim(), parts[1].Trim());
        }
    }
}

Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/sma-trend-follower-.log",
                  rollingInterval: RollingInterval.Day,
                  retainedFileCountLimit: 30)
    .CreateLogger();

try
{
    // Parse command line arguments for safety controls
    var dryRun = args.Contains("--dry-run");
    var confirm = args.Contains("--confirm");
    var paperOnly = args.Contains("--paper-only");
    var liveOnly = args.Contains("--live-only");
    var showSafety = args.Contains("--show-safety");

    // Check if user wants to check account status
    if (args.Length > 0 && args[0].Equals("--check-account", StringComparison.OrdinalIgnoreCase))
    {
        await AccountChecker.CheckAccountAsync();
        return;
    }

    // Check if user wants to perform cache maintenance
    if (args.Length > 0 && args[0].Equals("--cache-maintenance", StringComparison.OrdinalIgnoreCase))
    {
        await PerformCacheMaintenanceAsync(args);
        return;
    }

    // Check if user wants to view cache report
    if (args.Length > 0 && args[0].Equals("--cache-report", StringComparison.OrdinalIgnoreCase))
    {
        await ShowCacheReportAsync();
        return;
    }

    // Check if user wants to test Discord notifications
    if (args.Length > 0 && args[0].Equals("--test-discord", StringComparison.OrdinalIgnoreCase))
    {
        await TestDiscordAsync();
        return;
    }

    // Check if user wants to check account status
    if (args.Length > 0 && args[0].Equals("--account-status", StringComparison.OrdinalIgnoreCase))
    {
        await CheckAccountStatusAsync();
        return;
    }

    Log.Information("SmaTrendFollower — manual one-shot run");

    if (dryRun)
        Log.Information("🛡️ DRY RUN MODE ENABLED - No actual trades will be executed");
    if (paperOnly)
        Log.Information("🛡️ PAPER TRADING ONLY MODE");
    if (liveOnly)
        Log.Information("⚠️ LIVE TRADING ONLY MODE");
    if (confirm)
        Log.Information("✅ Live trading confirmation provided");

    using IHost host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureServices(services =>
        {
            // HTTP client factory for Polygon with rate limiting
            services.AddHttpClient();

            // Rate limiting policies
            services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

            // Configure Polygon HTTP client with Polly policies
            services.AddHttpClient("polygon", client =>
            {
                client.BaseAddress = new Uri("https://api.polygon.io/");
                client.Timeout = TimeSpan.FromSeconds(30);
            })
            .AddPolicyHandler((serviceProvider, request) =>
            {
                var policyFactory = serviceProvider.GetRequiredService<IRateLimitPolicyFactory>();
                return policyFactory.CreatePolygonPolicy();
            });

            // Database and caching
            services.AddDbContext<IndexCacheDbContext>(options =>
                options.UseSqlite("Data Source=index_cache.db"));
            services.AddScoped<IIndexCacheService, IndexCacheService>();

            services.AddDbContext<StockBarCacheDbContext>(options =>
                options.UseSqlite("Data Source=stock_cache.db"));
            services.AddScoped<IStockBarCacheService, StockBarCacheService>();
            services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

            // infrastructure
            services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
            services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
            services.AddSingleton<IMarketDataService, MarketDataService>();
            services.AddSingleton<IStreamingDataService, StreamingDataService>();
            services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();
            services.AddSingleton<ITimeProvider, SystemTimeProvider>();
            services.AddSingleton<IUniverseProvider, FileUniverseProvider>();

            // safety services
            services.AddSingleton<ISafetyConfigurationService, SafetyConfigurationService>();
            services.AddSingleton<ITradingSafetyGuard, TradingSafetyGuard>();

            // strategy stack
            services.AddScoped<ISignalGenerator, EnhancedSignalGenerator>();
            services.AddScoped<IRiskManager, RiskManager>();
            services.AddScoped<IPortfolioGate, PortfolioGate>();
            services.AddScoped<IStopManager, StopManager>();

            // Enhanced services
            services.AddScoped<IVolatilityManager, VolatilityManager>();
            services.AddScoped<IOptionsStrategyManager, OptionsStrategyManager>();
            services.AddScoped<IDiscordNotificationService, DiscordNotificationService>();

            // Register trade executor with safety wrapper
            services.AddScoped<TradeExecutor>(); // Original executor
            services.AddScoped<ITradeExecutor>(provider =>
            {
                var innerExecutor = provider.GetRequiredService<TradeExecutor>();
                var safetyGuard = provider.GetRequiredService<ITradingSafetyGuard>();
                var logger = provider.GetRequiredService<ILogger<SafeTradeExecutor>>();
                return new SafeTradeExecutor(innerExecutor, safetyGuard, logger);
            });

            services.AddScoped<ITradingService, EnhancedTradingService>();
        })
        .Build();

    using var scope   = host.Services.CreateScope();

    // Configure safety settings based on command line arguments
    var safetyConfigService = scope.ServiceProvider.GetRequiredService<ISafetyConfigurationService>();
    var safetyGuard = scope.ServiceProvider.GetRequiredService<ITradingSafetyGuard>();

    var safetyConfig = safetyConfigService.LoadConfiguration();

    // Override configuration based on command line arguments
    if (dryRun || paperOnly || liveOnly || !confirm)
    {
        safetyConfig = safetyConfig with
        {
            DryRunMode = dryRun,
            RequireConfirmation = !confirm,
            AllowedEnvironment = paperOnly ? TradingEnvironment.Paper :
                               liveOnly ? TradingEnvironment.Live :
                               safetyConfig.AllowedEnvironment
        };
    }

    safetyGuard.UpdateConfiguration(safetyConfig);

    // Show safety configuration if requested
    if (showSafety)
    {
        Log.Information("=== SAFETY CONFIGURATION ===");
        Log.Information("Dry Run Mode: {DryRun}", safetyConfig.DryRunMode);
        Log.Information("Allowed Environment: {Environment}", safetyConfig.AllowedEnvironment);
        Log.Information("Require Confirmation: {RequireConfirmation}", safetyConfig.RequireConfirmation);
        Log.Information("Max Daily Loss: {MaxDailyLoss:C}", safetyConfig.MaxDailyLoss);
        Log.Information("Max Position Size: {MaxPositionSize:P2}", safetyConfig.MaxPositionSizePercent);
        Log.Information("Max Positions: {MaxPositions}", safetyConfig.MaxPositions);
        Log.Information("Max Daily Trades: {MaxDailyTrades}", safetyConfig.MaxDailyTrades);
        Log.Information("Min Account Equity: {MinEquity:C}", safetyConfig.MinAccountEquity);
        Log.Information("Max Single Trade Value: {MaxTradeValue:C}", safetyConfig.MaxSingleTradeValue);
        return;
    }

    // Initialize the cache databases
    var indexCacheService = scope.ServiceProvider.GetRequiredService<IIndexCacheService>();
    await indexCacheService.InitializeCacheAsync();

    var stockCacheService = scope.ServiceProvider.GetRequiredService<IStockBarCacheService>();
    await stockCacheService.InitializeCacheAsync();

    var guard         = scope.ServiceProvider.GetRequiredService<IMarketSessionGuard>();

    if (!await guard.CanTradeNowAsync())
    {
        Log.Information("Exiting — {Reason}", guard.Reason);
        return;
    }

    var trader = scope.ServiceProvider.GetRequiredService<ITradingService>();
    await trader.ExecuteCycleAsync();

    Log.Information("Trading cycle completed ✓");
}
catch (Exception ex)
{
    Log.Fatal(ex, "Fatal error — bot aborted");
}
finally
{
    Log.CloseAndFlush();
}

static async Task PerformCacheMaintenanceAsync(string[] args)
{
    Log.Information("Performing cache maintenance");

    // Parse retention days from command line (default: 365)
    int retainDays = 365;
    if (args.Length > 1 && int.TryParse(args[1], out var parsedDays) && parsedDays > 0)
    {
        retainDays = parsedDays;
    }

    using IHost host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureServices(services =>
        {
            services.AddHttpClient();
            services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

            services.AddDbContext<IndexCacheDbContext>(options =>
                options.UseSqlite("Data Source=index_cache.db"));
            services.AddScoped<IIndexCacheService, IndexCacheService>();

            services.AddDbContext<StockBarCacheDbContext>(options =>
                options.UseSqlite("Data Source=stock_cache.db"));
            services.AddScoped<IStockBarCacheService, StockBarCacheService>();
            services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();
        })
        .Build();

    using var scope = host.Services.CreateScope();
    var maintenanceService = scope.ServiceProvider.GetRequiredService<ICacheMaintenanceService>();

    await maintenanceService.PerformMaintenanceAsync(retainDays);
    Log.Information("Cache maintenance completed successfully");
}

static async Task ShowCacheReportAsync()
{
    Log.Information("Generating cache report");

    using IHost host = Host.CreateDefaultBuilder()
        .UseSerilog()
        .ConfigureServices(services =>
        {
            services.AddHttpClient();
            services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

            services.AddDbContext<IndexCacheDbContext>(options =>
                options.UseSqlite("Data Source=index_cache.db"));
            services.AddScoped<IIndexCacheService, IndexCacheService>();

            services.AddDbContext<StockBarCacheDbContext>(options =>
                options.UseSqlite("Data Source=stock_cache.db"));
            services.AddScoped<IStockBarCacheService, StockBarCacheService>();
            services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();
        })
        .Build();

    using var scope = host.Services.CreateScope();
    var maintenanceService = scope.ServiceProvider.GetRequiredService<ICacheMaintenanceService>();

    var report = await maintenanceService.GetCacheReportAsync();

    Log.Information("=== Cache Report ===");
    Log.Information("Stock Cache Entries: {StockEntries}", report.StockCacheEntries);
    Log.Information("Total Stock Bars: {TotalBars:N0}", report.TotalStockBars);
    Log.Information("Stock Symbols: {SymbolCount}", report.StockSymbolCount);
    Log.Information("Average Bars per Symbol: {AvgBars:F1}", report.AverageBarsPerSymbol);
    Log.Information("Total Cache Size: {SizeMB:F1} MB", report.TotalSizeMB);
    Log.Information("Generated: {GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC", report.GeneratedAt);
}

static async Task TestDiscordAsync()
{
    Log.Information("Testing Discord notification service");

    using IHost host = Host.CreateDefaultBuilder()
        .UseSerilog()
        .ConfigureServices(services =>
        {
            services.AddHttpClient();
            services.AddScoped<IDiscordNotificationService, DiscordNotificationService>();
        })
        .Build();

    using var scope = host.Services.CreateScope();
    var discordService = scope.ServiceProvider.GetRequiredService<IDiscordNotificationService>();

    try
    {
        Log.Information("Sending test portfolio snapshot...");
        await discordService.SendPortfolioSnapshotAsync(
            totalEquity: 125000m,
            dayPnl: 1250m,
            totalPnl: 5000m,
            positionCount: 8
        );
        Log.Information("✅ Portfolio snapshot sent successfully");

        Log.Information("Sending test trade notification...");
        await discordService.SendTradeNotificationAsync(
            symbol: "SPY",
            action: "BUY",
            quantity: 10m,
            price: 450.25m,
            pnl: 0m
        );
        Log.Information("✅ Trade notification sent successfully");

        Log.Information("Sending test VIX spike alert...");
        await discordService.SendVixSpikeAlertAsync(
            currentVix: 28.5m,
            threshold: 25.0m,
            action: "Reducing position sizes"
        );
        Log.Information("✅ VIX spike alert sent successfully");

        Log.Information("🎉 All Discord tests completed successfully!");
        Log.Information("✅ Daily Reports are now ACTIVE!");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "❌ Discord test failed");
    }
}

static async Task CheckAccountStatusAsync()
{
    Log.Information("Checking Alpaca account status");

    using IHost host = Host.CreateDefaultBuilder()
        .UseSerilog()
        .ConfigureServices(services =>
        {
            services.AddHttpClient();
            services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
        })
        .Build();

    using var scope = host.Services.CreateScope();
    var clientFactory = scope.ServiceProvider.GetRequiredService<IAlpacaClientFactory>();

    try
    {
        using var tradingClient = clientFactory.CreateTradingClient();

        Log.Information("Fetching account information...");
        var account = await tradingClient.GetAccountAsync();

        Log.Information("=== ACCOUNT STATUS ===");
        Log.Information("Account ID: {AccountId}", account.AccountId);
        Log.Information("Status: {Status}", account.Status);
        Log.Information("Equity: {Equity:C}", account.Equity);
        Log.Information("Cash: {Cash:C}", account.TradableCash);
        Log.Information("Buying Power: {BuyingPower:C}", account.BuyingPower);
        Log.Information("Day Trading Buying Power: {DayTradingBuyingPower:C}", account.DayTradingBuyingPower);
        Log.Information("Last Equity: {LastEquity:C}", account.LastEquity);

        Log.Information("Fetching current positions...");
        var positions = await tradingClient.ListPositionsAsync();
        var positionList = positions.ToList();

        Log.Information("=== POSITIONS ===");
        Log.Information("Total Positions: {PositionCount}", positionList.Count);

        if (positionList.Any())
        {
            foreach (var position in positionList.Take(10)) // Show first 10 positions
            {
                Log.Information("  {Symbol}: {Quantity} shares @ {AvgPrice:C} (P&L: {UnrealizedPnL:C})",
                    position.Symbol, position.Quantity, position.AverageEntryPrice, position.UnrealizedProfitLoss);
            }

            if (positionList.Count > 10)
            {
                Log.Information("  ... and {MoreCount} more positions", positionList.Count - 10);
            }
        }

        Log.Information("✅ Account status check completed successfully!");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "❌ Account status check failed");
    }
}
