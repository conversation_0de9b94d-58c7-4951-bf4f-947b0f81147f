using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for sending Discord bot notifications using bot token and channel ID
/// </summary>
public sealed class DiscordNotificationService : IDiscordNotificationService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<DiscordNotificationService> _logger;
    private readonly string? _botToken;
    private readonly string? _channelId;

    public DiscordNotificationService(HttpClient httpClient, ILogger<DiscordNotificationService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _botToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
        _channelId = Environment.GetEnvironmentVariable("DISCORD_CHANNEL_ID");

        // Set up authorization header if bot token is available
        if (!string.IsNullOrEmpty(_botToken))
        {
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bot", _botToken);
        }
    }

    public async Task SendTradeNotificationAsync(string symbol, string action, decimal quantity, decimal price, decimal pnl)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping trade notification");
            return;
        }

        try
        {
            var emoji = action.ToUpper() switch
            {
                "BUY" => "🟢",
                "SELL" => "🔴",
                "STOP" => "🛑",
                _ => "📊"
            };

            var pnlEmoji = pnl >= 0 ? "💰" : "📉";
            var pnlText = pnl != 0 ? $" | P&L: {pnlEmoji} ${pnl:N2}" : "";

            var message = $"{emoji} **{action.ToUpper()}** {symbol}\n" +
                         $"📈 Quantity: {quantity:N2}\n" +
                         $"💵 Price: ${price:N2}{pnlText}";

            await SendDiscordMessageAsync("Trade Execution", message, GetTradeColor(action));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending trade notification to Discord");
        }
    }

    public async Task SendPortfolioSnapshotAsync(decimal totalEquity, decimal dayPnl, decimal totalPnl, int positionCount)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping portfolio snapshot");
            return;
        }

        try
        {
            var dayPnlEmoji = dayPnl >= 0 ? "📈" : "📉";
            var totalPnlEmoji = totalPnl >= 0 ? "💰" : "🔻";

            var message = $"💼 **Portfolio Snapshot**\n" +
                         $"💵 Total Equity: ${totalEquity:N2}\n" +
                         $"{dayPnlEmoji} Day P&L: ${dayPnl:N2} ({dayPnl/totalEquity:P2})\n" +
                         $"{totalPnlEmoji} Total P&L: ${totalPnl:N2}\n" +
                         $"📊 Active Positions: {positionCount}";

            var color = dayPnl >= 0 ? 0x00FF00 : 0xFF0000; // Green for profit, red for loss
            await SendDiscordMessageAsync("Portfolio Update", message, color);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending portfolio snapshot to Discord");
        }
    }

    public async Task SendVixSpikeAlertAsync(decimal currentVix, decimal threshold, string action)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping VIX spike alert");
            return;
        }

        try
        {
            var message = $"⚠️ **VIX SPIKE ALERT** ⚠️\n" +
                         $"📊 Current VIX: {currentVix:N1}\n" +
                         $"🚨 Threshold: {threshold:N1}\n" +
                         $"🎯 Action: {action}";

            await SendDiscordMessageAsync("VIX Spike Alert", message, 0xFF6600); // Orange color
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending VIX spike alert to Discord");
        }
    }

    public async Task SendOptionsNotificationAsync(string strategy, string symbol, string details)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
        {
            _logger.LogDebug("Discord bot token or channel ID not configured, skipping options notification");
            return;
        }

        try
        {
            var emoji = strategy.ToLower() switch
            {
                "protective put" => "🛡️",
                "covered call" => "📞",
                "delta efficient" => "⚡",
                _ => "🎯"
            };

            var message = $"{emoji} **{strategy.ToUpper()}** - {symbol}\n" +
                         $"📋 {details}";

            await SendDiscordMessageAsync("Options Strategy", message, 0x9932CC); // Purple color
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending options notification to Discord");
        }
    }

    private async Task SendDiscordMessageAsync(string title, string description, int color)
    {
        if (string.IsNullOrEmpty(_botToken) || string.IsNullOrEmpty(_channelId))
            return;

        try
        {
            var embed = new
            {
                title = title,
                description = description,
                color = color,
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                footer = new
                {
                    text = "SmaTrendFollower Bot",
                    icon_url = "https://cdn.discordapp.com/embed/avatars/0.png"
                }
            };

            var payload = new
            {
                embeds = new[] { embed }
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Use Discord API endpoint for sending messages to a specific channel
            var apiUrl = $"https://discord.com/api/v10/channels/{_channelId}/messages";
            var response = await _httpClient.PostAsync(apiUrl, content);

            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Discord API returned {StatusCode}: {ReasonPhrase}. Response: {Response}",
                    response.StatusCode, response.ReasonPhrase, responseContent);
            }
            else
            {
                _logger.LogDebug("Successfully sent Discord notification: {Title}", title);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending Discord message");
        }
    }

    private static int GetTradeColor(string action)
    {
        return action.ToUpper() switch
        {
            "BUY" => 0x00FF00,    // Green
            "SELL" => 0xFF0000,   // Red
            "STOP" => 0xFF6600,   // Orange
            _ => 0x0099FF         // Blue
        };
    }
}
