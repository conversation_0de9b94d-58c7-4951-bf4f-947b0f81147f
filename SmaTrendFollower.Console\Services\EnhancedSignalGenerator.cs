using SmaTrendFollower.Console.Extensions;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced signal generator with VIX analysis and volatility filtering
/// </summary>
public sealed class EnhancedSignalGenerator : ISignalGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly IVolatilityManager _volatilityManager;
    private readonly ILogger<EnhancedSignalGenerator> _logger;

    public EnhancedSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        IVolatilityManager volatilityManager,
        ILogger<EnhancedSignalGenerator> logger)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _volatilityManager = volatilityManager;
        _logger = logger;
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10)
    {
        try
        {
            // Get current volatility regime
            var vixRegime = await _volatilityManager.GetCurrentRegimeAsync();
            _logger.LogInformation("Current VIX regime: {RegimeName}, VIX: {CurrentVix}, Position multiplier: {Multiplier}",
                vixRegime.RegimeName, vixRegime.CurrentVix, vixRegime.PositionSizeMultiplier);

            // Adjust topN based on volatility regime
            var adjustedTopN = vixRegime.IsHighVol ? Math.Max(1, topN / 2) : topN;
            _logger.LogInformation("Adjusted position count from {Original} to {Adjusted} due to volatility regime",
                topN, adjustedTopN);

            // Get universe of symbols with ADV filtering
            var symbols = await _marketDataService.GetUniverseWithAdvFilterAsync();
            var symbolList = symbols.ToList();

            _logger.LogInformation("Screening {Count} symbols for trading signals (ADV > $20M)", symbolList.Count);

            var signals = new List<EnhancedTradingSignal>();

            // Process symbols in batches to avoid API limits
            const int batchSize = 10;
            for (int i = 0; i < symbolList.Count; i += batchSize)
            {
                var batch = symbolList.Skip(i).Take(batchSize);
                var batchSignals = await ProcessBatchWithVixAnalysis(batch, vixRegime);
                signals.AddRange(batchSignals);
            }

            // Enhanced filtering with volatility considerations
            var filteredSignals = signals
                .Where(s => s.Price > 0 && s.Atr > 0)
                .Where(s => s.Atr / s.Price < 0.03m) // ATR/Price < 3% (volatility throttle)
                .Where(s => !vixRegime.IsVixSpike || s.VolatilityRank < 0.7m) // Avoid high-vol stocks during VIX spikes
                .OrderByDescending(s => s.SixMonthReturn * s.VixAdjustment) // VIX-adjusted ranking
                .Take(adjustedTopN)
                .ToList();

            // Convert to standard TradingSignal format
            var standardSignals = filteredSignals.Select(s => new TradingSignal(
                s.Symbol, s.Price, s.Atr, s.SixMonthReturn)).ToList();

            _logger.LogInformation("Generated {Count} enhanced trading signals from {Total} symbols (VIX regime: {Regime})",
                standardSignals.Count, symbolList.Count, vixRegime.RegimeName);

            return standardSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating enhanced trading signals");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    private async Task<IEnumerable<EnhancedTradingSignal>> ProcessBatchWithVixAnalysis(
        IEnumerable<string> symbols, VolatilityRegime vixRegime)
    {
        var signals = new List<EnhancedTradingSignal>();

        foreach (var symbol in symbols)
        {
            try
            {
                // Get 250 days of data for technical analysis
                var startDate = DateTime.UtcNow.AddDays(-300);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();

                if (bars.Count < 200) // Need enough data for SMA200
                    continue;

                var currentPrice = bars.Last().Close;
                var sma50 = (decimal)bars.GetSma50();
                var sma200 = (decimal)bars.GetSma200();
                var atr14 = (decimal)bars.GetAtr14();
                var sixMonthReturn = (decimal)bars.GetTotalReturn(126); // ~6 months

                // Calculate volatility rank (ATR percentile over 252 days)
                var volatilityRank = CalculateVolatilityRank(bars, atr14);

                // Primary trend filter: close > sma50 && close > sma200
                if (currentPrice > sma50 && currentPrice > sma200)
                {
                    // Calculate VIX adjustment factor
                    var vixAdjustment = CalculateVixAdjustment(vixRegime, volatilityRank);

                    signals.Add(new EnhancedTradingSignal(
                        symbol, currentPrice, atr14, sixMonthReturn,
                        vixAdjustment, vixRegime.IsVixSpike, volatilityRank));
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing symbol {Symbol}", symbol);
            }
        }

        return signals;
    }

    /// <summary>
    /// Calculates volatility rank (ATR percentile over lookback period)
    /// </summary>
    private static decimal CalculateVolatilityRank(IList<IBar> bars, decimal currentAtr)
    {
        if (bars.Count < 252) return 0.5m; // Default to median if insufficient data

        var atrValues = new List<decimal>();
        for (int i = 14; i < Math.Min(bars.Count, 252); i++)
        {
            var subset = bars.Skip(i - 14).Take(14).ToList();
            var atr = (decimal)subset.GetAtr14();
            atrValues.Add(atr);
        }

        if (!atrValues.Any()) return 0.5m;

        atrValues.Sort();
        var rank = atrValues.Count(atr => atr <= currentAtr) / (decimal)atrValues.Count;
        return Math.Max(0, Math.Min(1, rank));
    }

    /// <summary>
    /// Calculates VIX-based position adjustment factor
    /// </summary>
    private static decimal CalculateVixAdjustment(VolatilityRegime vixRegime, decimal volatilityRank)
    {
        // Base adjustment from VIX regime
        var baseAdjustment = vixRegime.PositionSizeMultiplier;

        // Additional adjustment based on individual stock volatility
        var volAdjustment = volatilityRank < 0.3m ? 1.1m : // Low vol stocks get boost
                           volatilityRank > 0.7m ? 0.8m : // High vol stocks get penalty
                           1.0m; // Medium vol stocks unchanged

        // VIX spike penalty
        var spikeAdjustment = vixRegime.IsVixSpike ? 0.7m : 1.0m;

        return baseAdjustment * volAdjustment * spikeAdjustment;
    }
}
