using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class RiskManager : IRiskManager
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IVolatilityManager _volatilityManager;
    private readonly ILogger<RiskManager> _logger;

    public RiskManager(
        IAlpacaClientFactory clientFactory,
        IVolatilityManager volatilityManager,
        ILogger<RiskManager> logger)
    {
        _clientFactory = clientFactory;
        _volatilityManager = volatilityManager;
        _logger = logger;
    }

    public async Task<decimal> CalculateQuantityAsync(TradingSignal signal)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Get account information
                var account = await tradingClient.GetAccountAsync();

                // Get VIX-based position adjustment
                var vixAdjustment = await _volatilityManager.GetVixPositionAdjustmentAsync();

                // Calculate base risk dollars: min(account.Equity * 0.01m, 1000m) for 10bps per $100k cap
                var equity = account.Equity ?? 0m;
                var baseRiskDollars = Math.Min(equity * 0.01m, 1000m);

                // Apply VIX adjustment to risk dollars
                var adjustedRiskDollars = baseRiskDollars * vixAdjustment;

                // Calculate quantity: qty = adjustedRiskDollars / (atr14 * price)
                var quantity = adjustedRiskDollars / (signal.Atr * signal.Price);

                // Ensure quantity doesn't exceed risk tolerance: qty <= adjustedRiskDollars / price
                var maxQuantity = adjustedRiskDollars / signal.Price;
                quantity = Math.Min(quantity, maxQuantity);

                // Round down to avoid over-allocation
                quantity = Math.Floor(quantity * 100) / 100; // Round to 2 decimal places

                _logger.LogInformation("Enhanced risk calculation for {Symbol}: Equity={Equity:C}, " +
                                     "BaseRisk={BaseRisk:C}, VixAdjustment={VixAdj:P1}, AdjustedRisk={AdjustedRisk:C}, " +
                                     "Price={Price:C}, ATR={ATR:C}, Quantity={Quantity}",
                    signal.Symbol, equity, baseRiskDollars, vixAdjustment - 1, adjustedRiskDollars,
                    signal.Price, signal.Atr, quantity);

                return Math.Max(0, quantity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating position size for {Symbol}", signal.Symbol);
                return 0;
            }
        }, $"CalculateQuantity-{signal.Symbol}");
    }
}
