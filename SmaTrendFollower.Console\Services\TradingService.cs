using Microsoft.Extensions.Logging;
using Serilog;

namespace SmaTrendFollower.Services;

public sealed class TradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    private readonly ITradeExecutor _executor;
    private readonly IStopManager _stopManager;
    private readonly ITradingSafetyGuard _safetyGuard;

    public TradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate,
        ITradeExecutor executor,
        IStopManager stopManager,
        ITradingSafetyGuard safetyGuard)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _portfolioGate = portfolioGate;
        _executor = executor;
        _stopManager = stopManager;
        _safetyGuard = safetyGuard;
    }

    public async Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        // Validate trading cycle safety first
        Log.Information("Performing trading cycle safety validation");
        var cycleValidation = await _safetyGuard.ValidateTradingCycleAsync();

        if (!cycleValidation.IsAllowed)
        {
            var logLevel = cycleValidation.Level switch
            {
                SafetyLevel.Critical => LogLevel.Critical,
                SafetyLevel.Error => LogLevel.Error,
                SafetyLevel.Warning => LogLevel.Warning,
                _ => LogLevel.Information
            };

            Log.Logger.Write((Serilog.Events.LogEventLevel)logLevel,
                "Trading cycle blocked by safety guard: {Reason}", cycleValidation.Reason);
            return;
        }

        Log.Information("Trading cycle safety validation passed: {Reason}", cycleValidation.Reason);

        // First, update trailing stops for existing positions (capital preservation)
        Log.Information("Updating trailing stops for existing positions");
        await _stopManager.UpdateTrailingStopsAsync(cancellationToken);

        // Check if we should trade today (SPY SMA200 check)
        if (!await _portfolioGate.ShouldTradeAsync())
        {
            Log.Information("Portfolio gate blocked trading - SPY below SMA200");
            return;
        }

        // Generate trading signals
        var signals = await _signalGenerator.RunAsync(10);

        foreach (var signal in signals)
        {
            var quantity = await _riskManager.CalculateQuantityAsync(signal);
            if (quantity > 0)
            {
                await _executor.ExecuteTradeAsync(signal, quantity);
            }
        }

        Log.Information("Trading cycle completed");
    }
}
