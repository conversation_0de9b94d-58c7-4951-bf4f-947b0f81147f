﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>SmaTrendFollower.Console</AssemblyName>
    <RootNamespace>SmaTrendFollower</RootNamespace>
  </PropertyGroup>

<ItemGroup>
  <!-- Alpaca SDK -->
  <PackageReference Include="Alpaca.Markets" Version="7.2.0" />

  <!-- Microsoft hosting / DI -->
  <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
  <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
  <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />

  <!-- Entity Framework / SQLite -->
  <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />

  <!-- Serilog -->
  <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
  <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />  <!-- NEW -->

  <!-- Utilities -->
  <PackageReference Include="DotNetEnv" Version="2.4.0" />
  <PackageReference Include="Skender.Stock.Indicators" Version="2.6.1" />
  <PackageReference Include="System.Text.Json" Version="9.0.6" />
  <PackageReference Include="TimeZoneConverter" Version="3.3.0" />
  <PackageReference Include="System.IO.Compression" Version="4.3.0" />
</ItemGroup>


</Project>
