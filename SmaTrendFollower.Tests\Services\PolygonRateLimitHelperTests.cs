using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class PolygonRateLimitHelperTests
{
    private readonly Mock<ILogger<PolygonRateLimitHelper>> _mockLogger;
    private readonly PolygonRateLimitHelper _helper;

    public PolygonRateLimitHelperTests()
    {
        _mockLogger = new Mock<ILogger<PolygonRateLimitHelper>>();
        _helper = new PolygonRateLimitHelper(_mockLogger.Object);
    }

    [Fact]
    public async Task ExecuteAsync_HttpCall_ShouldExecuteSuccessfulOperation()
    {
        // Arrange
        var expectedResponse = new HttpResponseMessage(HttpStatusCode.OK);

        // Act
        var result = await _helper.ExecuteAsync(async () =>
        {
            await Task.Delay(10);
            return expectedResponse;
        }, "TestHttpOperation");

        // Assert
        result.Should().Be(expectedResponse);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task ExecuteAsync_GenericCall_ShouldExecuteSuccessfulOperation()
    {
        // Arrange
        var expectedResult = "success";

        // Act
        var result = await _helper.ExecuteAsync(async () =>
        {
            await Task.Delay(10);
            return expectedResult;
        }, "TestGenericOperation");

        // Assert
        result.Should().Be(expectedResult);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldRetryOnRateLimitException()
    {
        // Arrange
        var callCount = 0;
        var expectedResult = "success";

        // Act
        var result = await _helper.ExecuteAsync(() =>
        {
            callCount++;
            if (callCount == 1)
            {
                throw new Exception("TooManyRequests");
            }
            return Task.FromResult(expectedResult);
        }, "TestOperation");

        // Assert
        result.Should().Be(expectedResult);
        callCount.Should().Be(2);
    }

    [Fact]
    public async Task ExecuteAsync_HttpCall_ShouldRetryOnTooManyRequests()
    {
        // Arrange
        var callCount = 0;

        // Act
        var result = await _helper.ExecuteAsync(() =>
        {
            callCount++;
            if (callCount == 1)
            {
                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.TooManyRequests));
            }
            return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
        }, "TestHttpOperation");

        // Assert
        result.StatusCode.Should().Be(HttpStatusCode.OK);
        callCount.Should().Be(2);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldHandleConcurrentRequests()
    {
        // Arrange
        var tasks = new List<Task<string>>();
        
        // Act
        for (int i = 0; i < 5; i++) // Polygon has 5 req/sec limit
        {
            var taskIndex = i;
            tasks.Add(_helper.ExecuteAsync(async () =>
            {
                await Task.Delay(50);
                return $"result-{taskIndex}";
            }, $"ConcurrentOperation-{taskIndex}"));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(5);
        results.Should().OnlyContain(r => r.StartsWith("result-"));
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _helper.Dispose();
        action.Should().NotThrow();
    }
}
