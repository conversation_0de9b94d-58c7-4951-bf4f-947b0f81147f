using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class StreamingDataServiceTests
{
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<ILogger<StreamingDataService>> _mockLogger;
    private readonly Mock<IAlpacaStreamingClient> _mockAlpacaStreamingClient;
    private readonly Mock<IAlpacaDataStreamingClient> _mockAlpacaDataStreamingClient;
    private readonly Mock<IPolygonWebSocketClient> _mockPolygonWebSocketClient;
    private readonly StreamingDataService _service;

    public StreamingDataServiceTests()
    {
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockLogger = new Mock<ILogger<StreamingDataService>>();
        _mockAlpacaStreamingClient = new Mock<IAlpacaStreamingClient>();
        _mockAlpacaDataStreamingClient = new Mock<IAlpacaDataStreamingClient>();
        _mockPolygonWebSocketClient = new Mock<IPolygonWebSocketClient>();

        // Setup factory methods
        _mockAlpacaFactory.Setup(f => f.CreateStreamingClient()).Returns(_mockAlpacaStreamingClient.Object);
        _mockAlpacaFactory.Setup(f => f.CreateDataStreamingClient()).Returns(_mockAlpacaDataStreamingClient.Object);
        _mockPolygonFactory.Setup(f => f.CreateWebSocketClient()).Returns(_mockPolygonWebSocketClient.Object);

        _service = new StreamingDataService(
            _mockAlpacaFactory.Object,
            _mockPolygonFactory.Object,
            _mockLogger.Object);
    }

    [Fact]
    public void Constructor_ShouldInitializeWithDisconnectedStatus()
    {
        // Assert
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [Fact]
    public async Task ConnectAlpacaStreamAsync_ShouldCreateClientsAndConnect()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));

        // Act
        await _service.ConnectAlpacaStreamAsync();

        // Assert
        _mockAlpacaFactory.Verify(f => f.CreateStreamingClient(), Times.Once);
        _mockAlpacaFactory.Verify(f => f.CreateDataStreamingClient(), Times.Once);
        _mockAlpacaStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockAlpacaDataStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Connected);
    }

    [Fact]
    public async Task ConnectAlpacaStreamAsync_WhenAlreadyConnected_ShouldNotReconnect()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));

        // First connection
        await _service.ConnectAlpacaStreamAsync();

        // Act - Second connection attempt
        await _service.ConnectAlpacaStreamAsync();

        // Assert - Should only connect once
        _mockAlpacaStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockAlpacaDataStreamingClient.Verify(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ConnectAlpacaStreamAsync_WhenConnectionFails_ShouldSetErrorStatus()
    {
        // Arrange
        var exception = new Exception("Connection failed");
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).ThrowsAsync(exception);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _service.ConnectAlpacaStreamAsync());
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Error);
    }

    [Fact]
    public async Task ConnectPolygonStreamAsync_ShouldCreateClientAndConnect()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);

        // Act
        await _service.ConnectPolygonStreamAsync();

        // Assert
        _mockPolygonFactory.Verify(f => f.CreateWebSocketClient(), Times.Once);
        _mockPolygonWebSocketClient.Verify(c => c.ConnectAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SubscribeToQuotesAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToQuotesAsync(symbols));
    }

    [Fact]
    public async Task SubscribeToQuotesAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToQuotesAsync(symbols);

        // Assert - Since we're using placeholder implementation, just verify no exception is thrown
        // In real implementation, this would verify actual subscription calls
    }

    [Fact]
    public async Task SubscribeToBarsAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToBarsAsync(symbols));
    }

    [Fact]
    public async Task SubscribeToBarsAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToBarsAsync(symbols);

        // Assert - Since we're using placeholder implementation, just verify no exception is thrown
        // In real implementation, this would verify actual subscription calls
    }

    [Fact]
    public async Task SubscribeToTradeUpdatesAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToTradeUpdatesAsync());
    }

    [Fact]
    public async Task SubscribeToTradeUpdatesAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));

        await _service.ConnectAlpacaStreamAsync();

        // Act
        await _service.SubscribeToTradeUpdatesAsync();

        // Assert - Since we're using placeholder implementation, just verify no exception is thrown
        // In real implementation, this would verify actual subscription calls
    }

    [Fact]
    public async Task SubscribeToIndexUpdatesAsync_WithoutConnection_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _service.SubscribeToIndexUpdatesAsync(symbols));
    }

    [Fact]
    public async Task SubscribeToIndexUpdatesAsync_WithConnection_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.SubscribeToIndexUpdatesAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        await _service.ConnectPolygonStreamAsync();

        // Act
        await _service.SubscribeToIndexUpdatesAsync(symbols);

        // Assert
        _mockPolygonWebSocketClient.Verify(c => c.SubscribeToIndexUpdatesAsync(symbols, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task DisconnectAllAsync_ShouldDisconnectAllClients()
    {
        // Arrange
        _mockAlpacaStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockAlpacaDataStreamingClient.Setup(c => c.ConnectAndAuthenticateAsync(It.IsAny<CancellationToken>())).Returns(Task.FromResult(AuthStatus.Authorized));
        _mockPolygonWebSocketClient.Setup(c => c.ConnectAsync(It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);

        _mockAlpacaStreamingClient.Setup(c => c.DisconnectAsync(It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);
        _mockAlpacaDataStreamingClient.Setup(c => c.DisconnectAsync(It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);
        _mockPolygonWebSocketClient.Setup(c => c.DisconnectAsync(It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);

        await _service.ConnectAlpacaStreamAsync();
        await _service.ConnectPolygonStreamAsync();

        // Act
        await _service.DisconnectAllAsync();

        // Assert
        _mockAlpacaStreamingClient.Verify(c => c.DisconnectAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockAlpacaDataStreamingClient.Verify(c => c.DisconnectAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockPolygonWebSocketClient.Verify(c => c.DisconnectAsync(It.IsAny<CancellationToken>()), Times.Once);
        _service.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [Fact]
    public async Task UnsubscribeAllAsync_ShouldUnsubscribeFromAllStreams()
    {
        // Arrange
        _mockPolygonWebSocketClient.Setup(c => c.UnsubscribeAllAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _service.UnsubscribeAllAsync();

        // Assert - Should not throw even without connections
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        _service.Dispose(); // Should not throw
    }

    [Fact]
    public void Events_ShouldBeRaisable()
    {
        // Arrange
        StreamingQuoteEventArgs? quoteArgs = null;
        StreamingBarEventArgs? barArgs = null;
        IndexUpdateEventArgs? indexArgs = null;
        TradeUpdateEventArgs? tradeArgs = null;

        _service.QuoteReceived += (sender, args) => quoteArgs = args;
        _service.BarReceived += (sender, args) => barArgs = args;
        _service.IndexUpdated += (sender, args) => indexArgs = args;
        _service.TradeUpdated += (sender, args) => tradeArgs = args;

        // Act - Events would be triggered by actual data
        // For testing, we verify they can be subscribed to

        // Assert
        quoteArgs.Should().BeNull(); // No events fired yet
        barArgs.Should().BeNull();
        indexArgs.Should().BeNull();
        tradeArgs.Should().BeNull();
    }
}
