using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class TradeExecutorTests
{
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IStopManager> _mockStopManager;
    private readonly Mock<IVolatilityManager> _mockVolatilityManager;
    private readonly Mock<IDiscordNotificationService> _mockDiscordService;
    private readonly Mock<ILogger<TradeExecutor>> _mockLogger;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<IAlpacaRateLimitHelper> _mockRateLimitHelper;
    private readonly TradeExecutor _tradeExecutor;

    public TradeExecutorTests()
    {
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockStopManager = new Mock<IStopManager>();
        _mockVolatilityManager = new Mock<IVolatilityManager>();
        _mockDiscordService = new Mock<IDiscordNotificationService>();
        _mockLogger = new Mock<ILogger<TradeExecutor>>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockRateLimitHelper = new Mock<IAlpacaRateLimitHelper>();

        _mockClientFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockClientFactory.Setup(x => x.GetRateLimitHelper()).Returns(_mockRateLimitHelper.Object);

        // Setup volatility manager to return the entry price - 2*ATR as stop price
        _mockVolatilityManager.Setup(x => x.GetIvAdjustedStopAsync(It.IsAny<string>(), It.IsAny<decimal>(), It.IsAny<decimal>()))
            .Returns<string, decimal, decimal>((symbol, entryPrice, atr) => Task.FromResult(entryPrice - (2m * atr)));

        _tradeExecutor = new TradeExecutor(_mockClientFactory.Object, _mockStopManager.Object,
            _mockVolatilityManager.Object, _mockDiscordService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithValidSignal_ShouldSubmitBuyOrder()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m);
        var quantity = 10m;

        var mockOrder = new Mock<IOrder>();
        mockOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockOrder.Object);

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder>());

        _mockRateLimitHelper.Setup(x => x.ExecuteAsync<object>(It.IsAny<Func<Task<object>>>(), It.IsAny<string>()))
            .Returns<Func<Task<object>>, string>((func, name) => func());

        // Act
        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockTradingClient.Verify(x => x.PostOrderAsync(
            It.Is<NewOrderRequest>(order =>
                order.Symbol == "AAPL" &&
                order.Quantity == 10 &&
                order.Side == OrderSide.Buy &&
                order.Type == OrderType.Limit &&
                order.LimitPrice == 150.30m), // 150.00 * 1.002
            It.IsAny<CancellationToken>()), Times.Once);

        _mockStopManager.Verify(x => x.SetInitialStopAsync("AAPL", 150.30m, 2.50m, quantity, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithZeroQuantity_ShouldSkipTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m);
        var quantity = 0m;

        // Act
        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockTradingClient.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockStopManager.Verify(x => x.SetInitialStopAsync(It.IsAny<string>(), It.IsAny<decimal>(), It.IsAny<decimal>(), It.IsAny<decimal>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithNegativeQuantity_ShouldSkipTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m);
        var quantity = -5m;

        // Act
        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockTradingClient.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockStopManager.Verify(x => x.SetInitialStopAsync(It.IsAny<string>(), It.IsAny<decimal>(), It.IsAny<decimal>(), It.IsAny<decimal>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithFractionalQuantity_ShouldRoundUpToWholeShares()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m);
        var quantity = 2.7m; // Should round up to 3

        var mockOrder = new Mock<IOrder>();
        mockOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockOrder.Object);

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder>());

        _mockRateLimitHelper.Setup(x => x.ExecuteAsync<object>(It.IsAny<Func<Task<object>>>(), It.IsAny<string>()))
            .Returns<Func<Task<object>>, string>((func, name) => func());

        // Act
        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockTradingClient.Verify(x => x.PostOrderAsync(
            It.Is<NewOrderRequest>(order => order.Quantity == 3), // Rounded up from 2.7
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WithExistingOrders_ShouldCancelThemFirst()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m);
        var quantity = 10m;

        var existingOrder = new Mock<IOrder>();
        existingOrder.Setup(x => x.Symbol).Returns("AAPL");
        existingOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        var mockNewOrder = new Mock<IOrder>();
        mockNewOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), default))
            .ReturnsAsync(new List<IOrder> { existingOrder.Object });

        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), default))
            .ReturnsAsync(mockNewOrder.Object);

        _mockRateLimitHelper.Setup(x => x.ExecuteAsync<object>(It.IsAny<Func<Task<object>>>(), It.IsAny<string>()))
            .Returns<Func<Task<object>>, string>((func, name) => func());

        // Act
        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockTradingClient.Verify(x => x.CancelOrderAsync(existingOrder.Object.OrderId, It.IsAny<CancellationToken>()), Times.Once);
        _mockTradingClient.Verify(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ExecuteTradeAsync_WhenOrderSubmissionFails_ShouldLogError()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m);
        var quantity = 10m;

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), default))
            .ReturnsAsync(new List<IOrder>());

        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), default))
            .ThrowsAsync(new Exception("Order submission failed"));

        _mockRateLimitHelper.Setup(x => x.ExecuteAsync<object>(It.IsAny<Func<Task<object>>>(), It.IsAny<string>()))
            .Returns<Func<Task<object>>, string>((func, name) => func());

        // Act
        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error executing trade for AAPL")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteTradeAsync_ShouldCalculateCorrectEntryAndStopPrices()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", 100.00m, 3.00m, 0.15m);
        var quantity = 5m;

        var mockOrder = new Mock<IOrder>();
        mockOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());

        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockOrder.Object);

        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IOrder>());

        _mockRateLimitHelper.Setup(x => x.ExecuteAsync<object>(It.IsAny<Func<Task<object>>>(), It.IsAny<string>()))
            .Returns<Func<Task<object>>, string>((func, name) => func());

        _mockStopManager.Setup(x => x.SetInitialStopAsync(It.IsAny<string>(), It.IsAny<decimal>(), It.IsAny<decimal>(), It.IsAny<decimal>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await _tradeExecutor.ExecuteTradeAsync(signal, quantity);

        // Assert
        var expectedEntryPrice = 100.00m * 1.002m; // 100.20
        
        _mockTradingClient.Verify(x => x.PostOrderAsync(
            It.Is<NewOrderRequest>(order => order.LimitPrice == expectedEntryPrice),
            It.IsAny<CancellationToken>()), Times.Once);

        _mockStopManager.Verify(x => x.SetInitialStopAsync("AAPL", expectedEntryPrice, 3.00m, quantity, It.IsAny<CancellationToken>()), Times.Once);
    }
}
